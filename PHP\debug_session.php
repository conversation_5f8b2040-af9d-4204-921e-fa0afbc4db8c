<?php
session_start();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Session Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .session-info { background: #f0f0f0; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .admin-login { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Session Debug Information</h1>
    
    <div class="session-info">
        <h3>Current Session Data:</h3>
        <pre><?php print_r($_SESSION); ?></pre>
    </div>
    
    <div class="session-info">
        <h3>Session Status:</h3>
        <p><strong>Session ID:</strong> <?= session_id() ?></p>
        <p><strong>Logged In:</strong> <?= isset($_SESSION["loggedin"]) ? ($_SESSION["loggedin"] ? 'Yes' : 'No') : 'Not Set' ?></p>
        <p><strong>Is Admin:</strong> <?= isset($_SESSION["is_admin"]) ? ($_SESSION["is_admin"] ? 'Yes' : 'No') : 'Not Set' ?></p>
        <p><strong>User ID:</strong> <?= $_SESSION["id"] ?? 'Not Set' ?></p>
        <p><strong>Username:</strong> <?= $_SESSION["username"] ?? 'Not Set' ?></p>
    </div>
    
    <?php if (!isset($_SESSION["loggedin"]) || !$_SESSION["loggedin"] || !isset($_SESSION["is_admin"]) || !$_SESSION["is_admin"]): ?>
    <div class="admin-login">
        <h3>Admin Login Required</h3>
        <p>You need to login as admin to access admin pages.</p>
        <form method="post" action="">
            <p><strong>Username:</strong> admin</p>
            <p><strong>Password:</strong> admin123</p>
            <button type="button" onclick="loginAsAdmin()">Login as Admin</button>
        </form>
    </div>
    <?php else: ?>
    <div class="admin-login">
        <h3 class="success">✓ Admin Access Granted</h3>
        <p>You are logged in as admin and can access admin pages.</p>
        <p><a href="admin.php">Go to Admin Dashboard</a></p>
        <p><a href="admin_profile.php">Go to Admin Profile</a></p>
        <p><a href="admin_pc_management.php">Go to PC Management</a></p>
        <p><a href="admin_credit_dashboard.php">Go to Credit Dashboard</a></p>
        <p><a href="member.php">Go to Member Page</a></p>
    </div>
    <?php endif; ?>
    
    <script>
    function loginAsAdmin() {
        fetch('login.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'username=admin&password=admin123'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Admin login successful!');
                location.reload();
            } else {
                alert('Login failed: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Login error occurred');
        });
    }
    </script>
</body>
</html>
