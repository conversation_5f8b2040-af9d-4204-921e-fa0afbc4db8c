<?php
session_start();

// Force admin login for testing
$_SESSION["loggedin"] = true;
$_SESSION["id"] = 1;
$_SESSION["username"] = 'admin';
$_SESSION["is_admin"] = true;

require_once 'config.php';
?>
<!DOCTYPE html>
<html>
<head>
    <title>Comprehensive Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .test-section h3 { color: #007bff; margin-top: 0; }
        .result { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .loading { color: #6c757d; font-style: italic; }
        pre { background: #f8f8f8; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Comprehensive Test Report</h1>
        <p><strong>Test Time:</strong> <?= date('Y-m-d H:i:s') ?></p>
        
        <div class="test-section">
            <h3>1. Database Connection Test</h3>
            <div id="db-test">
                <?php
                if ($link) {
                    echo '<div class="result success">✅ Database connection successful</div>';
                    
                    // Test basic query
                    $test_query = "SELECT 1 as test";
                    $result = mysqli_query($link, $test_query);
                    if ($result) {
                        echo '<div class="result success">✅ Database queries working</div>';
                        mysqli_free_result($result);
                    } else {
                        echo '<div class="result error">❌ Database query failed: ' . mysqli_error($link) . '</div>';
                    }
                } else {
                    echo '<div class="result error">❌ Database connection failed</div>';
                }
                ?>
            </div>
        </div>
        
        <div class="test-section">
            <h3>2. Session Management Test</h3>
            <div class="result info">
                <strong>Session Data:</strong><br>
                Logged in: <?= isset($_SESSION["loggedin"]) ? ($_SESSION["loggedin"] ? 'Yes' : 'No') : 'Not Set' ?><br>
                Is Admin: <?= isset($_SESSION["is_admin"]) ? ($_SESSION["is_admin"] ? 'Yes' : 'No') : 'Not Set' ?><br>
                User ID: <?= $_SESSION["id"] ?? 'Not Set' ?><br>
                Username: <?= $_SESSION["username"] ?? 'Not Set' ?>
            </div>
        </div>
        
        <div class="test-section">
            <h3>3. Function Availability Test</h3>
            <div id="function-test">
                <?php
                $functions_to_test = ['execute_query', 'sanitize_input', 'get_db_connection'];
                foreach ($functions_to_test as $func) {
                    if (function_exists($func)) {
                        echo '<div class="result success">✅ Function ' . $func . '() exists</div>';
                    } else {
                        echo '<div class="result error">❌ Function ' . $func . '() missing</div>';
                    }
                }
                ?>
            </div>
        </div>
        
        <div class="test-section">
            <h3>4. Database Tables Test</h3>
            <div id="tables-test">
                <?php
                $required_tables = [
                    'users', 'pc_component_categories', 'pc_components', 
                    'pc_prebuilt_configs', 'pc_orders', 'user_wallets', 
                    'credit_transactions', 'deposit_records', 'credit_audit_log'
                ];
                
                foreach ($required_tables as $table) {
                    $check_sql = "SHOW TABLES LIKE '$table'";
                    $result = mysqli_query($link, $check_sql);
                    if (mysqli_num_rows($result) > 0) {
                        echo '<div class="result success">✅ Table ' . $table . ' exists</div>';
                    } else {
                        echo '<div class="result error">❌ Table ' . $table . ' missing</div>';
                    }
                    mysqli_free_result($result);
                }
                ?>
            </div>
        </div>
        
        <div class="test-section">
            <h3>5. API Endpoints Test</h3>
            <button onclick="testAPIs()">Test All APIs</button>
            <div id="api-results"></div>
        </div>
        
        <div class="test-section">
            <h3>6. Page Loading Test</h3>
            <button onclick="testPages()">Test All Pages</button>
            <div id="page-results"></div>
        </div>
        
        <div class="test-section">
            <h3>7. Quick Access Links</h3>
            <div>
                <a href="admin.php" target="_blank" style="display: inline-block; padding: 8px 15px; background: #28a745; color: white; text-decoration: none; border-radius: 4px; margin: 5px;">Admin Dashboard</a>
                <a href="admin_profile.php" target="_blank" style="display: inline-block; padding: 8px 15px; background: #17a2b8; color: white; text-decoration: none; border-radius: 4px; margin: 5px;">Admin Profile</a>
                <a href="admin_pc_management.php" target="_blank" style="display: inline-block; padding: 8px 15px; background: #ffc107; color: black; text-decoration: none; border-radius: 4px; margin: 5px;">PC Management</a>
                <a href="member.php" target="_blank" style="display: inline-block; padding: 8px 15px; background: #6f42c1; color: white; text-decoration: none; border-radius: 4px; margin: 5px;">Member Page</a>
                <a href="admin_credit_dashboard.php" target="_blank" style="display: inline-block; padding: 8px 15px; background: #fd7e14; color: white; text-decoration: none; border-radius: 4px; margin: 5px;">Credit Dashboard</a>
            </div>
        </div>
    </div>
    
    <script>
    function testAPIs() {
        const resultDiv = document.getElementById('api-results');
        resultDiv.innerHTML = '<div class="loading">Testing APIs...</div>';
        
        const apis = [
            { url: 'pc_components_api.php?action=get_categories', name: 'Get Categories' },
            { url: 'pc_components_api.php?action=get_components_by_category&active_only=1', name: 'Get Components' },
            { url: 'pc_components_api.php?action=get_prebuilt_configs&active_only=1', name: 'Get Prebuilt Configs' },
            { url: 'admin_credit.php?action=get_system_stats', name: 'Credit System Stats' },
            { url: 'admin_credit.php?action=get_all_wallets&page=1&limit=10', name: 'Get Wallets' }
        ];
        
        let results = '';
        let completed = 0;
        
        apis.forEach(api => {
            fetch(api.url)
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        results += `<div class="result success">✅ ${api.name}: Success</div>`;
                    } else {
                        results += `<div class="result error">❌ ${api.name}: ${data.message || 'Failed'}</div>`;
                    }
                } catch (e) {
                    results += `<div class="result error">❌ ${api.name}: JSON Parse Error<br><pre>${text.substring(0, 200)}...</pre></div>`;
                }
                
                completed++;
                if (completed === apis.length) {
                    resultDiv.innerHTML = results;
                }
            })
            .catch(error => {
                results += `<div class="result error">❌ ${api.name}: Network Error - ${error.message}</div>`;
                completed++;
                if (completed === apis.length) {
                    resultDiv.innerHTML = results;
                }
            });
        });
    }
    
    function testPages() {
        const resultDiv = document.getElementById('page-results');
        resultDiv.innerHTML = '<div class="loading">Testing pages...</div>';
        
        const pages = [
            { url: 'admin_profile.php', name: 'Admin Profile' },
            { url: 'admin_pc_management.php', name: 'PC Management' },
            { url: 'member.php', name: 'Member Page' },
            { url: 'admin_credit_dashboard.php', name: 'Credit Dashboard' }
        ];
        
        let results = '';
        let completed = 0;
        
        pages.forEach(page => {
            fetch(page.url)
            .then(response => response.text())
            .then(html => {
                if (html.includes('Fatal error') || html.includes('Parse error')) {
                    results += `<div class="result error">❌ ${page.name}: PHP Error Found</div>`;
                } else if (html.includes('<!DOCTYPE html>')) {
                    results += `<div class="result success">✅ ${page.name}: Loads Successfully</div>`;
                } else {
                    results += `<div class="result warning">⚠️ ${page.name}: Unexpected Response</div>`;
                }
                
                completed++;
                if (completed === pages.length) {
                    resultDiv.innerHTML = results;
                }
            })
            .catch(error => {
                results += `<div class="result error">❌ ${page.name}: Network Error - ${error.message}</div>`;
                completed++;
                if (completed === pages.length) {
                    resultDiv.innerHTML = results;
                }
            });
        });
    }
    </script>
</body>
</html>
