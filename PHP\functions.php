<?php
require_once 'config.php';

function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// Get the total view count from the database.
function get_total_view_count() {
    global $link;
    $count = 0;
    $sql_count = "SELECT COUNT(*) as total_views FROM page_views";
    $result = mysqli_query($link, $sql_count);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        $count = $row['total_views'];
        mysqli_free_result($result);
    }
    return $count;
}

// Log a new page view and then return the total count.
function get_and_update_view_count() {
    global $link;

    $user_id = null;
    $user_type = 'guest';
    if (isset($_SESSION["loggedin"]) && $_SESSION["loggedin"] === true) {
        $user_id = intval($_SESSION["id"]);
        $user_type = isset($_SESSION["is_admin"]) && $_SESSION["is_admin"] === true ? 'admin' : 'member';
    }

    // Ensure we have valid data with fallbacks
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    $page_name = basename($_SERVER['PHP_SELF']) ?? 'index';

    // Handle NULL user_id properly - use different query based on whether user is logged in
    if ($user_id === null) {
        $sql = "INSERT INTO page_views (page_name, user_id, user_type, ip_address, user_agent) VALUES (?, NULL, ?, ?, ?)";
        $stmt = execute_query($link, $sql, "ssss", [$page_name, $user_type, $ip_address, $user_agent]);
    } else {
        $sql = "INSERT INTO page_views (page_name, user_id, user_type, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)";
        $stmt = execute_query($link, $sql, "sisss", [$page_name, $user_id, $user_type, $ip_address, $user_agent]);
    }
    
    if ($stmt) {
        mysqli_stmt_close($stmt);
    }

    return get_total_view_count();
}

/**
 * Load translations for a given language
 *
 * @param string $lang The language code (e.g., 'en', 'zh-CN')
 * @return array The translations array
 */
function loadTranslations($lang) {
    $lang_file_path = __DIR__ . '/../lang/' . $lang . '.json';
    $translations = [];
    
    if (file_exists($lang_file_path)) {
        $translations = json_decode(file_get_contents($lang_file_path), true);
    } else {
        // Fallback to default language if the file is missing for some reason
        $default_lang_file_path = __DIR__ . '/../lang/en.json';
        if (file_exists($default_lang_file_path)) {
            $translations = json_decode(file_get_contents($default_lang_file_path), true);
        }
    }
    
    return $translations ?? [];
}

// Function to execute prepared queries
function execute_query($connection, $sql, $types = "", $params = []) {
    $stmt = mysqli_prepare($connection, $sql);
    if (!$stmt) {
        return false;
    }

    if (!empty($params) && !empty($types)) {
        mysqli_stmt_bind_param($stmt, $types, ...$params);
    }

    if (!mysqli_stmt_execute($stmt)) {
        mysqli_stmt_close($stmt);
        return false;
    }

    return $stmt;
}

// Function to get database connection
function get_db_connection() {
    global $link;
    return $link;
}

// Function to close database connection
function close_db_connection($connection) {
    // We don't actually close the global connection
    // This is just for API compatibility
    return true;
}