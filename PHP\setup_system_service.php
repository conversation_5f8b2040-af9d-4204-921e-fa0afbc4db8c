<?php
// Setup System Installation Service
require_once 'config.php';

echo "<h2>Setting up System Installation Service...</h2>";

try {
    // Read and execute the SQL file
    $sql_file = '../SQL/add_system_service.sql';
    
    if (!file_exists($sql_file)) {
        throw new Exception("SQL file not found: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    
    // Remove comments and split by semicolon
    $sql_statements = array_filter(
        array_map('trim', explode(';', $sql_content)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^(--|USE|\/\*)/', $stmt);
        }
    );
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($sql_statements as $sql) {
        if (empty(trim($sql))) continue;
        
        echo "<p>Executing: " . substr($sql, 0, 100) . "...</p>";
        
        if (mysqli_query($link, $sql)) {
            echo "<p style='color: green;'>✓ Success</p>";
            $success_count++;
        } else {
            echo "<p style='color: red;'>✗ Error: " . mysqli_error($link) . "</p>";
            $error_count++;
        }
    }
    
    echo "<h3>Setup Complete!</h3>";
    echo "<p>Successful operations: $success_count</p>";
    echo "<p>Failed operations: $error_count</p>";
    
    // Verify the service was created
    $check_service = "SELECT * FROM pc_services WHERE is_mandatory = 1";
    $result = mysqli_query($link, $check_service);
    
    if ($result && mysqli_num_rows($result) > 0) {
        echo "<h4>Mandatory System Service:</h4>";
        while ($service = mysqli_fetch_assoc($result)) {
            echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>" . htmlspecialchars($service['service_name']) . "</strong><br>";
            echo "Price: $" . number_format($service['current_price'], 2) . "<br>";
            echo "Description: " . htmlspecialchars($service['description']) . "<br>";
            echo "Mandatory: " . ($service['is_mandatory'] ? 'Yes' : 'No') . "<br>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

mysqli_close($link);
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
p { margin: 5px 0; }
</style>
