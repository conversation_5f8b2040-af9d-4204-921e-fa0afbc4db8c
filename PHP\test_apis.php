<?php
session_start();

// Set admin session for testing
$_SESSION["loggedin"] = true;
$_SESSION["id"] = 0;
$_SESSION["username"] = 'admin';
$_SESSION["is_admin"] = true;
?>
<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { background: #f0f0f0; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .result { background: #e8f5e8; padding: 10px; border-radius: 3px; margin: 5px 0; }
        .error { background: #ffe8e8; color: red; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>API Testing</h1>
    
    <div class="test-section">
        <h3>Session Status</h3>
        <p>Logged in: <?= $_SESSION["loggedin"] ? 'Yes' : 'No' ?></p>
        <p>Is Admin: <?= $_SESSION["is_admin"] ? 'Yes' : 'No' ?></p>
        <p>User ID: <?= $_SESSION["id"] ?></p>
    </div>
    
    <div class="test-section">
        <h3>PC Components API Tests</h3>
        <button onclick="testAPI('pc_components_api.php?action=get_categories', 'categories')">Test Get Categories</button>
        <button onclick="testAPI('pc_components_api.php?action=get_components&active_only=1', 'components')">Test Get Components</button>
        <button onclick="testAPI('pc_components_api.php?action=get_components_by_category&active_only=1', 'components_by_category')">Test Get Components by Category</button>
        <button onclick="testAPI('pc_components_api.php?action=get_prebuilt_configs&active_only=1', 'prebuilt_configs')">Test Get Prebuilt Configs</button>
        <button onclick="testAPI('pc_components_api.php?action=admin_get_pc_orders', 'pc_orders')">Test Get PC Orders</button>
        <div id="pc-api-results"></div>
    </div>
    
    <div class="test-section">
        <h3>Credit API Tests</h3>
        <button onclick="testAPI('admin_credit.php?action=get_all_wallets&page=1&limit=20', 'credit_wallets')">Test Get Wallets</button>
        <button onclick="testAPI('admin_credit.php?action=get_system_stats', 'credit_stats')">Test Get System Stats</button>
        <button onclick="testAPI('admin_credit.php?action=get_all_transactions&page=1&limit=50', 'credit_transactions')">Test Get Transactions</button>
        <div id="credit-api-results"></div>
    </div>
    
    <div class="test-section">
        <h3>Database Tables Check</h3>
        <button onclick="checkTables()">Check Database Tables</button>
        <div id="table-results"></div>
    </div>
    
    <script>
    function testAPI(url, testName) {
        const resultDiv = document.getElementById(url.includes('pc_components') ? 'pc-api-results' : 'credit-api-results');
        
        fetch(url, {
            credentials: 'same-origin'
        })
        .then(response => response.text())
        .then(text => {
            try {
                const data = JSON.parse(text);
                resultDiv.innerHTML += `
                    <div class="result">
                        <strong>${testName}:</strong> 
                        ${data.success ? '✓ Success' : '✗ Failed: ' + data.message}
                        <br><small>Data: ${JSON.stringify(data).substring(0, 200)}...</small>
                    </div>
                `;
            } catch (e) {
                resultDiv.innerHTML += `
                    <div class="result error">
                        <strong>${testName}:</strong> ✗ JSON Parse Error
                        <br><small>Response: ${text.substring(0, 500)}...</small>
                    </div>
                `;
            }
        })
        .catch(error => {
            resultDiv.innerHTML += `
                <div class="result error">
                    <strong>${testName}:</strong> ✗ Network Error: ${error.message}
                </div>
            `;
        });
    }
    
    function checkTables() {
        fetch('check_pc_tables.php')
        .then(response => response.text())
        .then(html => {
            document.getElementById('table-results').innerHTML = `
                <div class="result">
                    <strong>Table Check Results:</strong><br>
                    <iframe srcdoc="${html.replace(/"/g, '&quot;')}" style="width: 100%; height: 300px; border: 1px solid #ccc;"></iframe>
                </div>
            `;
        })
        .catch(error => {
            document.getElementById('table-results').innerHTML = `
                <div class="result error">
                    <strong>Table Check:</strong> ✗ Error: ${error.message}
                </div>
            `;
        });
    }
    </script>
</body>
</html>
