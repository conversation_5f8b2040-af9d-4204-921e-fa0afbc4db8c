<?php
/**
 * KMS Credit Admin Management API
 * Handles admin operations for credit system
 */

require_once 'config.php';
require_once 'credit_system.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['username'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Admin access required']);
    exit;
}

$admin_user_id = $_SESSION['user_id'];
$action = $_POST['action'] ?? $_GET['action'] ?? '';

header('Content-Type: application/json');

switch ($action) {
    case 'get_all_wallets':
        getAllWallets();
        break;
        
    case 'get_all_transactions':
        getAllTransactions();
        break;
        
    case 'get_pending_deposits':
        getPendingDeposits();
        break;
        
    case 'approve_deposit':
        approveDeposit($credit_system, $admin_user_id);
        break;
        
    case 'reject_deposit':
        rejectDeposit($admin_user_id);
        break;
        
    case 'manual_adjust':
        manualAdjustBalance($credit_system, $admin_user_id);
        break;

    case 'add_kms_credit':
        addKMSCredit($credit_system, $admin_user_id);
        break;
        
    case 'get_system_stats':
        getSystemStats();
        break;
        
    case 'update_payment_method':
        updatePaymentMethod();
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

/**
 * Get all user wallets
 */
function getAllWallets() {
    global $link;
    
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 50);
    $offset = ($page - 1) * $limit;
    $search = trim($_GET['search'] ?? '');
    
    $where_clause = '';
    $params = [];
    $types = '';
    
    if (!empty($search)) {
        $where_clause = "WHERE u.username LIKE ? OR u.email LIKE ?";
        $search_param = "%$search%";
        $params = [$search_param, $search_param];
        $types = 'ss';
    }
    
    $sql = "SELECT u.id, u.username, u.email, uw.balance, uw.frozen_balance, 
                   uw.total_deposited, uw.total_spent, uw.created_at as wallet_created,
                   uw.updated_at as wallet_updated
            FROM users u 
            LEFT JOIN user_wallets uw ON u.id = uw.user_id 
            $where_clause
            ORDER BY uw.balance DESC 
            LIMIT ? OFFSET ?";
    
    $params[] = $limit;
    $params[] = $offset;
    $types .= 'ii';
    
    $stmt = execute_query($link, $sql, $types, $params);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $result = mysqli_stmt_get_result($stmt);
    $wallets = [];
    
    while ($row = mysqli_fetch_assoc($result)) {
        $wallets[] = [
            'user_id' => $row['id'],
            'username' => $row['username'],
            'email' => $row['email'],
            'balance' => number_format($row['balance'] ?? 0, 2),
            'frozen_balance' => number_format($row['frozen_balance'] ?? 0, 2),
            'total_deposited' => number_format($row['total_deposited'] ?? 0, 2),
            'total_spent' => number_format($row['total_spent'] ?? 0, 2),
            'available_balance' => number_format(($row['balance'] ?? 0) - ($row['frozen_balance'] ?? 0), 2),
            'wallet_created' => $row['wallet_created'] ? date('Y-m-d H:i:s', strtotime($row['wallet_created'])) : null,
            'wallet_updated' => $row['wallet_updated'] ? date('Y-m-d H:i:s', strtotime($row['wallet_updated'])) : null
        ];
    }
    
    mysqli_stmt_close($stmt);
    
    echo json_encode([
        'success' => true,
        'wallets' => $wallets,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'has_more' => count($wallets) == $limit
        ]
    ]);
}

/**
 * Get all transactions
 */
function getAllTransactions() {
    global $link;
    
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 50);
    $offset = ($page - 1) * $limit;
    $type_filter = trim($_GET['type'] ?? '');
    $status_filter = trim($_GET['status'] ?? '');
    
    $where_conditions = [];
    $params = [];
    $types = '';
    
    if (!empty($type_filter)) {
        $where_conditions[] = "ct.transaction_type = ?";
        $params[] = $type_filter;
        $types .= 's';
    }
    
    if (!empty($status_filter)) {
        $where_conditions[] = "ct.status = ?";
        $params[] = $status_filter;
        $types .= 's';
    }
    
    $where_clause = '';
    if (!empty($where_conditions)) {
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    }
    
    $sql = "SELECT ct.*, u.username, admin_u.username as admin_username
            FROM credit_transactions ct
            JOIN users u ON ct.user_id = u.id
            LEFT JOIN users admin_u ON ct.admin_user_id = admin_u.id
            $where_clause
            ORDER BY ct.created_at DESC
            LIMIT ? OFFSET ?";
    
    $params[] = $limit;
    $params[] = $offset;
    $types .= 'ii';
    
    $stmt = execute_query($link, $sql, $types, $params);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $result = mysqli_stmt_get_result($stmt);
    $transactions = [];
    
    while ($row = mysqli_fetch_assoc($result)) {
        $transactions[] = [
            'id' => $row['id'],
            'transaction_id' => $row['transaction_id'],
            'username' => $row['username'],
            'type' => $row['transaction_type'],
            'amount' => number_format($row['amount'], 2),
            'balance_before' => number_format($row['balance_before'], 2),
            'balance_after' => number_format($row['balance_after'], 2),
            'status' => $row['status'],
            'description' => $row['description'],
            'payment_method' => $row['payment_method'],
            'admin_username' => $row['admin_username'],
            'created_at' => date('Y-m-d H:i:s', strtotime($row['created_at']))
        ];
    }
    
    mysqli_stmt_close($stmt);
    
    echo json_encode([
        'success' => true,
        'transactions' => $transactions,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'has_more' => count($transactions) == $limit
        ]
    ]);
}

/**
 * Get pending deposits
 */
function getPendingDeposits() {
    global $link;
    
    $sql = "SELECT dr.*, u.username, pm.display_name as payment_method_name 
            FROM deposit_records dr 
            JOIN users u ON dr.user_id = u.id 
            LEFT JOIN payment_methods pm ON dr.payment_method = pm.method_name 
            WHERE dr.payment_status IN ('pending', 'processing') 
            ORDER BY dr.created_at ASC";
    
    $result = mysqli_query($link, $sql);
    
    if (!$result) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $deposits = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $payment_details = null;
        if ($row['payment_details']) {
            $payment_details = json_decode($row['payment_details'], true);
        }
        
        $deposits[] = [
            'id' => $row['id'],
            'transaction_id' => $row['transaction_id'],
            'username' => $row['username'],
            'amount' => number_format($row['amount'], 2),
            'payment_method' => $row['payment_method_name'] ?? $row['payment_method'],
            'status' => $row['payment_status'],
            'payment_details' => $payment_details,
            'created_at' => date('Y-m-d H:i:s', strtotime($row['created_at'])),
            'external_payment_id' => $row['external_payment_id']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'pending_deposits' => $deposits
    ]);
}

/**
 * Approve a deposit
 */
function approveDeposit($credit_system, $admin_user_id) {
    $transaction_id = trim($_POST['transaction_id'] ?? '');
    $admin_notes = trim($_POST['admin_notes'] ?? '');
    
    if (empty($transaction_id)) {
        echo json_encode(['success' => false, 'message' => 'Transaction ID is required']);
        return;
    }
    
    global $link;
    
    mysqli_begin_transaction($link);
    
    try {
        // Get deposit details
        $sql = "SELECT dr.*, ct.user_id FROM deposit_records dr 
                JOIN credit_transactions ct ON dr.transaction_id = ct.transaction_id 
                WHERE dr.transaction_id = ? AND dr.payment_status IN ('pending', 'processing')";
        $stmt = execute_query($link, $sql, "s", [$transaction_id]);
        
        if (!$stmt) {
            throw new Exception('Database error');
        }
        
        $result = mysqli_stmt_get_result($stmt);
        $deposit = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$deposit) {
            throw new Exception('Deposit not found or already processed');
        }
        
        // Add credit to user's wallet
        $add_result = $credit_system->addCredit(
            $deposit['user_id'],
            $deposit['amount'],
            $deposit['payment_method'],
            "Admin approved deposit - " . $admin_notes,
            $deposit['external_payment_id'],
            $admin_user_id
        );
        
        if (!$add_result['success']) {
            throw new Exception($add_result['message']);
        }
        
        // Update deposit status
        $update_sql = "UPDATE deposit_records SET payment_status = 'completed', admin_notes = ?, processed_by = ? WHERE transaction_id = ?";
        $update_stmt = execute_query($link, $update_sql, "sis", [$admin_notes, $admin_user_id, $transaction_id]);
        
        if (!$update_stmt) {
            throw new Exception('Failed to update deposit status');
        }
        mysqli_stmt_close($update_stmt);
        
        mysqli_commit($link);
        
        echo json_encode([
            'success' => true,
            'message' => 'Deposit approved successfully'
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Reject a deposit
 */
function rejectDeposit($admin_user_id) {
    $transaction_id = trim($_POST['transaction_id'] ?? '');
    $admin_notes = trim($_POST['admin_notes'] ?? '');
    
    if (empty($transaction_id)) {
        echo json_encode(['success' => false, 'message' => 'Transaction ID is required']);
        return;
    }
    
    global $link;
    
    mysqli_begin_transaction($link);
    
    try {
        // Update deposit status
        $deposit_sql = "UPDATE deposit_records SET payment_status = 'failed', admin_notes = ?, processed_by = ? WHERE transaction_id = ? AND payment_status IN ('pending', 'processing')";
        $deposit_stmt = execute_query($link, $deposit_sql, "sis", [$admin_notes, $admin_user_id, $transaction_id]);
        
        if (!$deposit_stmt || mysqli_stmt_affected_rows($deposit_stmt) == 0) {
            throw new Exception('Deposit not found or already processed');
        }
        mysqli_stmt_close($deposit_stmt);
        
        // Update transaction status
        $trans_sql = "UPDATE credit_transactions SET status = 'failed' WHERE transaction_id = ?";
        $trans_stmt = execute_query($link, $trans_sql, "s", [$transaction_id]);
        
        if (!$trans_stmt) {
            throw new Exception('Failed to update transaction status');
        }
        mysqli_stmt_close($trans_stmt);
        
        mysqli_commit($link);
        
        echo json_encode([
            'success' => true,
            'message' => 'Deposit rejected successfully'
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Manual balance adjustment
 */
function manualAdjustBalance($credit_system, $admin_user_id) {
    $user_id = intval($_POST['user_id'] ?? 0);
    $amount = floatval($_POST['amount'] ?? 0);
    $reason = trim($_POST['reason'] ?? '');
    
    if ($user_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid user ID']);
        return;
    }
    
    if ($amount == 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid adjustment amount']);
        return;
    }
    
    if (empty($reason)) {
        echo json_encode(['success' => false, 'message' => 'Reason is required']);
        return;
    }
    
    if ($amount > 0) {
        // Add credit
        $result = $credit_system->addCredit($user_id, $amount, 'admin', "Admin adjustment: " . $reason, null, $admin_user_id);
    } else {
        // Deduct credit
        $result = $credit_system->deductCredit($user_id, abs($amount), "Admin adjustment: " . $reason, 'admin', null);
    }
    
    echo json_encode($result);
}

/**
 * Add KMS Credit with source tracking
 */
function addKMSCredit($credit_system, $admin_user_id) {
    $user_id = intval($_POST['user_id'] ?? 0);
    $amount = floatval($_POST['amount'] ?? 0);
    $source = trim($_POST['source'] ?? '');
    $reason = trim($_POST['reason'] ?? '');

    if ($user_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid user ID']);
        return;
    }

    if ($amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Amount must be positive']);
        return;
    }

    if (empty($source)) {
        echo json_encode(['success' => false, 'message' => 'Payment source is required']);
        return;
    }

    if (empty($reason)) {
        echo json_encode(['success' => false, 'message' => 'Transaction reference/notes are required']);
        return;
    }

    // Create detailed description with source tracking
    $description = "KMS Credit Added - Source: {$source} | Reference: {$reason}";

    // Add credit with source tracking
    $result = $credit_system->addCredit(
        $user_id,
        $amount,
        $source,
        $description,
        null,
        $admin_user_id
    );

    if ($result['success']) {
        // Log additional audit information
        global $link;
        $audit_sql = "INSERT INTO credit_audit_log (user_id, admin_id, action_type, amount, source_type, reference_notes, created_at) VALUES (?, ?, 'add_credit', ?, ?, ?, NOW())";
        $audit_stmt = mysqli_prepare($link, $audit_sql);
        if ($audit_stmt) {
            mysqli_stmt_bind_param($audit_stmt, "iidss", $user_id, $admin_user_id, $amount, $source, $reason);
            mysqli_stmt_execute($audit_stmt);
            mysqli_stmt_close($audit_stmt);
        }

        $result['message'] = "KMS Credit added successfully with source tracking";
    }

    echo json_encode($result);
}

/**
 * Get system statistics
 */
function getSystemStats() {
    global $link;
    
    // Total users with wallets
    $total_users_sql = "SELECT COUNT(*) as count FROM user_wallets";
    $total_users_result = mysqli_query($link, $total_users_sql);
    $total_users = mysqli_fetch_assoc($total_users_result)['count'];
    
    // Total balance in system
    $total_balance_sql = "SELECT SUM(balance) as total FROM user_wallets";
    $total_balance_result = mysqli_query($link, $total_balance_sql);
    $total_balance = mysqli_fetch_assoc($total_balance_result)['total'] ?? 0;
    
    // Total deposits
    $total_deposits_sql = "SELECT SUM(total_deposited) as total FROM user_wallets";
    $total_deposits_result = mysqli_query($link, $total_deposits_sql);
    $total_deposits = mysqli_fetch_assoc($total_deposits_result)['total'] ?? 0;
    
    // Total spent
    $total_spent_sql = "SELECT SUM(total_spent) as total FROM user_wallets";
    $total_spent_result = mysqli_query($link, $total_spent_sql);
    $total_spent = mysqli_fetch_assoc($total_spent_result)['total'] ?? 0;
    
    // Pending deposits
    $pending_deposits_sql = "SELECT COUNT(*) as count, SUM(amount) as total FROM deposit_records WHERE payment_status IN ('pending', 'processing')";
    $pending_deposits_result = mysqli_query($link, $pending_deposits_sql);
    $pending_deposits = mysqli_fetch_assoc($pending_deposits_result);
    
    // Recent transactions (last 7 days)
    $recent_transactions_sql = "SELECT COUNT(*) as count FROM credit_transactions WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
    $recent_transactions_result = mysqli_query($link, $recent_transactions_sql);
    $recent_transactions = mysqli_fetch_assoc($recent_transactions_result)['count'];
    
    echo json_encode([
        'success' => true,
        'stats' => [
            'total_users' => $total_users,
            'total_balance' => number_format($total_balance, 2),
            'total_deposits' => number_format($total_deposits, 2),
            'total_spent' => number_format($total_spent, 2),
            'pending_deposits_count' => $pending_deposits['count'],
            'pending_deposits_amount' => number_format($pending_deposits['total'] ?? 0, 2),
            'recent_transactions' => $recent_transactions
        ]
    ]);
}

/**
 * Update payment method settings
 */
function updatePaymentMethod() {
    $method_id = intval($_POST['method_id'] ?? 0);
    $is_active = isset($_POST['is_active']) ? (bool)$_POST['is_active'] : null;
    $min_amount = isset($_POST['min_amount']) ? floatval($_POST['min_amount']) : null;
    $max_amount = isset($_POST['max_amount']) ? floatval($_POST['max_amount']) : null;
    $fee_percentage = isset($_POST['fee_percentage']) ? floatval($_POST['fee_percentage']) : null;
    $fee_fixed = isset($_POST['fee_fixed']) ? floatval($_POST['fee_fixed']) : null;
    
    if ($method_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid payment method ID']);
        return;
    }
    
    global $link;
    
    $updates = [];
    $params = [];
    $types = '';
    
    if ($is_active !== null) {
        $updates[] = "is_active = ?";
        $params[] = $is_active ? 1 : 0;
        $types .= 'i';
    }
    
    if ($min_amount !== null) {
        $updates[] = "min_amount = ?";
        $params[] = $min_amount;
        $types .= 'd';
    }
    
    if ($max_amount !== null) {
        $updates[] = "max_amount = ?";
        $params[] = $max_amount;
        $types .= 'd';
    }
    
    if ($fee_percentage !== null) {
        $updates[] = "fee_percentage = ?";
        $params[] = $fee_percentage;
        $types .= 'd';
    }
    
    if ($fee_fixed !== null) {
        $updates[] = "fee_fixed = ?";
        $params[] = $fee_fixed;
        $types .= 'd';
    }
    
    if (empty($updates)) {
        echo json_encode(['success' => false, 'message' => 'No updates provided']);
        return;
    }
    
    $sql = "UPDATE payment_methods SET " . implode(', ', $updates) . " WHERE id = ?";
    $params[] = $method_id;
    $types .= 'i';
    
    $stmt = execute_query($link, $sql, $types, $params);
    
    if ($stmt && mysqli_stmt_affected_rows($stmt) > 0) {
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'Payment method updated successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update payment method']);
    }
}
?>
