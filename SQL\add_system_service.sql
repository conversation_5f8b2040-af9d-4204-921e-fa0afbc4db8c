-- Add System Installation Service to PC Components
-- This service is mandatory for all PC builds

USE kelvinkms;

-- Create PC Services Table for additional services like system installation
CREATE TABLE IF NOT EXISTS pc_services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    service_name VARCHAR(200) NOT NULL,
    service_name_en VARCHAR(200) NOT NULL,
    service_name_zh VARCHAR(200) NOT NULL,
    description TEXT,
    description_en TEXT,
    description_zh TEXT,
    base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    is_mandatory BOOLEAN DEFAULT FALSE COMMENT 'Whether this service is required for all PC builds',
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_mandatory (is_mandatory),
    INDEX idx_sort (sort_order)
);

-- Insert the mandatory system installation service
INSERT INTO pc_services (
    service_name, 
    service_name_en, 
    service_name_zh, 
    description, 
    description_en, 
    description_zh, 
    base_price, 
    current_price, 
    is_mandatory, 
    is_active, 
    sort_order
) VALUES (
    'Complete System Setup Service',
    'Complete System Setup Service',
    '完整系統安裝服務',
    'Includes: System Installation + Windows Updates + Driver Installation + System Optimization + Stability Testing. This service is mandatory for all PC builds to ensure optimal performance and reliability.',
    'Includes: System Installation + Windows Updates + Driver Installation + System Optimization + Stability Testing. This service is mandatory for all PC builds to ensure optimal performance and reliability.',
    '包含：系統安裝 + Windows更新 + 驅動程式安裝 + 系統優化 + 穩定性測試。此服務為所有電腦組裝的必選項目，確保最佳性能和可靠性。',
    100.00,
    100.00,
    TRUE,
    TRUE,
    1
) ON DUPLICATE KEY UPDATE 
    service_name = VALUES(service_name),
    description = VALUES(description),
    current_price = VALUES(current_price);

-- Update pc_orders table to include services
ALTER TABLE pc_orders 
ADD COLUMN IF NOT EXISTS services JSON COMMENT 'Selected services for the PC build',
ADD COLUMN IF NOT EXISTS service_total DECIMAL(10,2) DEFAULT 0.00 COMMENT 'Total cost of selected services';

-- Create index for service_total
CREATE INDEX IF NOT EXISTS idx_service_total ON pc_orders(service_total);
