<?php
session_start();

// Set session for testing
$_SESSION["loggedin"] = true;
$_SESSION["id"] = 1;
$_SESSION["username"] = 'testuser';
$_SESSION["is_admin"] = false;
?>
<!DOCTYPE html>
<html>
<head>
    <title>Member API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { background: #f0f0f0; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .result { background: #e8f5e8; padding: 10px; border-radius: 3px; margin: 5px 0; }
        .error { background: #ffe8e8; color: red; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background: #f8f8f8; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Member API Testing</h1>
    
    <div class="test-section">
        <h3>Session Status</h3>
        <p>Logged in: <?= $_SESSION["loggedin"] ? 'Yes' : 'No' ?></p>
        <p>User ID: <?= $_SESSION["id"] ?></p>
        <p>Username: <?= $_SESSION["username"] ?></p>
    </div>
    
    <div class="test-section">
        <h3>PC Components API Tests</h3>
        <button onclick="testAPI('pc_components_api.php?action=get_categories', 'categories')">Test Get Categories</button>
        <button onclick="testAPI('pc_components_api.php?action=get_components_by_category&active_only=1', 'components_by_category')">Test Get Components by Category</button>
        <button onclick="testAPI('pc_components_api.php?action=get_prebuilt_configs&active_only=1', 'prebuilt_configs')">Test Get Prebuilt Configs</button>
        <div id="pc-api-results"></div>
    </div>
    
    <div class="test-section">
        <h3>Direct Database Check</h3>
        <button onclick="checkDatabase()">Check Database Tables</button>
        <div id="db-results"></div>
    </div>
    
    <script>
    function testAPI(url, testName) {
        const resultDiv = document.getElementById('pc-api-results');
        
        resultDiv.innerHTML += `<div>Testing ${testName}...</div>`;
        
        fetch(url, {
            credentials: 'same-origin'
        })
        .then(response => response.text())
        .then(text => {
            try {
                const data = JSON.parse(text);
                resultDiv.innerHTML += `
                    <div class="result">
                        <strong>${testName}:</strong> 
                        ${data.success ? '✓ Success' : '✗ Failed: ' + data.message}
                        <br><strong>Data count:</strong> ${Array.isArray(data.configs) ? data.configs.length : 
                                                          Array.isArray(data.categories) ? data.categories.length :
                                                          data.components_by_category ? Object.keys(data.components_by_category).length : 'N/A'}
                        <details>
                            <summary>Raw Response</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    </div>
                `;
            } catch (e) {
                resultDiv.innerHTML += `
                    <div class="result error">
                        <strong>${testName}:</strong> ✗ JSON Parse Error
                        <details>
                            <summary>Raw Response</summary>
                            <pre>${text}</pre>
                        </details>
                    </div>
                `;
            }
        })
        .catch(error => {
            resultDiv.innerHTML += `
                <div class="result error">
                    <strong>${testName}:</strong> ✗ Network Error: ${error.message}
                </div>
            `;
        });
    }
    
    function checkDatabase() {
        const resultDiv = document.getElementById('db-results');
        
        fetch('check_pc_tables.php')
        .then(response => response.text())
        .then(html => {
            resultDiv.innerHTML = `
                <div class="result">
                    <strong>Database Check Results:</strong><br>
                    <iframe srcdoc="${html.replace(/"/g, '&quot;')}" style="width: 100%; height: 400px; border: 1px solid #ccc;"></iframe>
                </div>
            `;
        })
        .catch(error => {
            resultDiv.innerHTML = `
                <div class="result error">
                    <strong>Database Check:</strong> ✗ Error: ${error.message}
                </div>
            `;
        });
    }
    </script>
</body>
</html>
