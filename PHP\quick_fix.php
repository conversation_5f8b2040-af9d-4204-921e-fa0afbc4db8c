<?php
session_start();

// Force admin login for testing
$_SESSION["loggedin"] = true;
$_SESSION["id"] = 1;
$_SESSION["username"] = 'admin';
$_SESSION["is_admin"] = true;

echo "<h2>Quick Fix Applied</h2>";
echo "<p>Admin session has been set. You can now access admin pages.</p>";
echo "<p><a href='admin.php'>Go to Admin Dashboard</a></p>";
echo "<p><a href='admin_profile.php'>Test Admin Profile</a></p>";
echo "<p><a href='admin_pc_management.php'>Test PC Management</a></p>";
echo "<p><a href='admin_credit_dashboard.php'>Test Credit Dashboard</a></p>";
echo "<p><a href='member.php'>Test Member Page</a></p>";

// Also check if we need to create admin user in database
require_once 'config.php';

// Check if admin user exists
$check_admin = "SELECT id FROM users WHERE username = 'admin'";
$result = mysqli_query($link, $check_admin);

if (mysqli_num_rows($result) == 0) {
    // Create admin user
    $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
    $create_admin = "INSERT INTO users (username, password, email, is_admin, created_at) VALUES ('admin', ?, '<EMAIL>', 1, NOW())";
    $stmt = mysqli_prepare($link, $create_admin);
    mysqli_stmt_bind_param($stmt, "s", $password_hash);
    
    if (mysqli_stmt_execute($stmt)) {
        echo "<p style='color: green;'>✓ Admin user created successfully</p>";
        $_SESSION["id"] = mysqli_insert_id($link);
    } else {
        echo "<p style='color: red;'>✗ Failed to create admin user: " . mysqli_error($link) . "</p>";
    }
    mysqli_stmt_close($stmt);
} else {
    $admin_data = mysqli_fetch_assoc($result);
    $_SESSION["id"] = $admin_data['id'];
    echo "<p style='color: green;'>✓ Admin user exists</p>";
}

mysqli_close($link);
?>
