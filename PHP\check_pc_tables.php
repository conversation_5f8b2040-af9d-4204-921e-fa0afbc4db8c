<?php
// Check and create PC management tables
require_once 'config.php';

echo "<h2>Checking PC Management Tables...</h2>";

try {
    // Check if tables exist
    $tables_to_check = [
        'pc_component_categories',
        'pc_components',
        'pc_prebuilt_configs',
        'pc_services',
        'pc_orders',
        'credit_audit_log'
    ];
    
    $existing_tables = [];
    $missing_tables = [];
    
    foreach ($tables_to_check as $table) {
        $result = mysqli_query($link, "SHOW TABLES LIKE '$table'");
        if (mysqli_num_rows($result) > 0) {
            $existing_tables[] = $table;
            echo "<p style='color: green;'>✓ Table '$table' exists</p>";
        } else {
            $missing_tables[] = $table;
            echo "<p style='color: red;'>✗ Table '$table' missing</p>";
        }
    }
    
    if (!empty($missing_tables)) {
        echo "<h3>Creating Missing Tables...</h3>";
        
        // Create pc_component_categories table
        if (in_array('pc_component_categories', $missing_tables)) {
            $sql = "CREATE TABLE pc_component_categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                category_name VARCHAR(100) NOT NULL,
                category_name_en VARCHAR(100) NOT NULL,
                category_name_zh VARCHAR(100) NOT NULL,
                description TEXT,
                description_en TEXT,
                description_zh TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                sort_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_active (is_active),
                INDEX idx_sort (sort_order)
            )";
            
            if (mysqli_query($link, $sql)) {
                echo "<p style='color: green;'>✓ Created pc_component_categories table</p>";
                
                // Insert default categories
                $categories = [
                    ['CPU', 'CPU', 'CPU', 'Central Processing Unit', 'Central Processing Unit', '中央處理器', 1],
                    ['GPU', 'GPU', 'GPU', 'Graphics Processing Unit', 'Graphics Processing Unit', '顯示卡', 2],
                    ['RAM', 'RAM', 'RAM', 'Random Access Memory', 'Random Access Memory', '記憶體', 3],
                    ['Storage', 'Storage', 'Storage', 'Storage Devices', 'Storage Devices', '儲存裝置', 4],
                    ['PSU', 'PSU', 'PSU', 'Power Supply Unit', 'Power Supply Unit', '電源供應器', 5]
                ];
                
                $insert_sql = "INSERT INTO pc_component_categories (category_name, category_name_en, category_name_zh, description, description_en, description_zh, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?)";
                $stmt = mysqli_prepare($link, $insert_sql);
                
                foreach ($categories as $cat) {
                    mysqli_stmt_bind_param($stmt, "ssssssi", $cat[0], $cat[1], $cat[2], $cat[3], $cat[4], $cat[5], $cat[6]);
                    mysqli_stmt_execute($stmt);
                }
                mysqli_stmt_close($stmt);
                echo "<p style='color: green;'>✓ Inserted default categories</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create pc_component_categories: " . mysqli_error($link) . "</p>";
            }
        }
        
        // Create pc_components table
        if (in_array('pc_components', $missing_tables)) {
            $sql = "CREATE TABLE pc_components (
                id INT AUTO_INCREMENT PRIMARY KEY,
                category_id INT NOT NULL,
                component_name VARCHAR(200) NOT NULL,
                component_name_en VARCHAR(200) NOT NULL,
                component_name_zh VARCHAR(200) NOT NULL,
                brand VARCHAR(100),
                model VARCHAR(100),
                specifications JSON,
                base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                description TEXT,
                description_en TEXT,
                description_zh TEXT,
                stock_quantity INT DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                sort_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES pc_component_categories(id),
                INDEX idx_category (category_id),
                INDEX idx_active (is_active),
                INDEX idx_price (current_price),
                INDEX idx_sort (sort_order)
            )";
            
            if (mysqli_query($link, $sql)) {
                echo "<p style='color: green;'>✓ Created pc_components table</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create pc_components: " . mysqli_error($link) . "</p>";
            }
        }
        
        // Create pc_prebuilt_configs table
        if (in_array('pc_prebuilt_configs', $missing_tables)) {
            $sql = "CREATE TABLE pc_prebuilt_configs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                config_name VARCHAR(200) NOT NULL,
                config_name_en VARCHAR(200) NOT NULL,
                config_name_zh VARCHAR(200) NOT NULL,
                description TEXT,
                description_en TEXT,
                description_zh TEXT,
                tier ENUM('entry', 'mid', 'high', 'extreme') DEFAULT 'entry',
                primary_use ENUM('gaming', 'work', 'both') DEFAULT 'gaming',
                components JSON,
                specifications_summary JSON,
                base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                discount_percentage DECIMAL(5,2) DEFAULT 0.00,
                is_active BOOLEAN DEFAULT TRUE,
                sort_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_tier (tier),
                INDEX idx_use (primary_use),
                INDEX idx_active (is_active),
                INDEX idx_price (current_price),
                INDEX idx_sort (sort_order)
            )";
            
            if (mysqli_query($link, $sql)) {
                echo "<p style='color: green;'>✓ Created pc_prebuilt_configs table</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create pc_prebuilt_configs: " . mysqli_error($link) . "</p>";
            }
        }
        
        // Create pc_services table
        if (in_array('pc_services', $missing_tables)) {
            $sql = "CREATE TABLE pc_services (
                id INT AUTO_INCREMENT PRIMARY KEY,
                service_name VARCHAR(200) NOT NULL,
                service_name_en VARCHAR(200) NOT NULL,
                service_name_zh VARCHAR(200) NOT NULL,
                description TEXT,
                description_en TEXT,
                description_zh TEXT,
                base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                is_mandatory BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                sort_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_active (is_active),
                INDEX idx_mandatory (is_mandatory),
                INDEX idx_sort (sort_order)
            )";
            
            if (mysqli_query($link, $sql)) {
                echo "<p style='color: green;'>✓ Created pc_services table</p>";
                
                // Insert mandatory system service
                $service_sql = "INSERT INTO pc_services (service_name, service_name_en, service_name_zh, description, description_en, description_zh, base_price, current_price, is_mandatory, is_active, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $service_stmt = mysqli_prepare($link, $service_sql);
                
                $name = 'Complete System Setup Service';
                $name_en = 'Complete System Setup Service';
                $name_zh = '完整系統安裝服務';
                $desc = 'Includes: System Installation + Windows Updates + Driver Installation + System Optimization + Stability Testing';
                $desc_en = 'Includes: System Installation + Windows Updates + Driver Installation + System Optimization + Stability Testing';
                $desc_zh = '包含：系統安裝 + Windows更新 + 驅動程式安裝 + 系統優化 + 穩定性測試';
                $base_price = 100.00;
                $current_price = 100.00;
                $is_mandatory = 1;
                $is_active = 1;
                $sort_order = 1;
                
                mysqli_stmt_bind_param($service_stmt, "ssssssddiiii", $name, $name_en, $name_zh, $desc, $desc_en, $desc_zh, $base_price, $current_price, $is_mandatory, $is_active, $sort_order);
                mysqli_stmt_execute($service_stmt);
                mysqli_stmt_close($service_stmt);
                
                echo "<p style='color: green;'>✓ Inserted mandatory system service</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create pc_services: " . mysqli_error($link) . "</p>";
            }
        }

        // Create pc_orders table
        if (in_array('pc_orders', $missing_tables)) {
            $sql = "CREATE TABLE pc_orders (
                id INT AUTO_INCREMENT PRIMARY KEY,
                order_number VARCHAR(50) UNIQUE NOT NULL,
                user_id INT NOT NULL,
                order_type ENUM('simple', 'detailed', 'prebuilt') NOT NULL,
                configuration JSON,
                components JSON,
                services JSON,
                prebuilt_config_id INT NULL,
                estimated_price DECIMAL(10,2) DEFAULT 0.00,
                final_price DECIMAL(10,2) DEFAULT 0.00,
                service_total DECIMAL(10,2) DEFAULT 0.00,
                admin_adjusted_price DECIMAL(10,2) NULL,
                admin_adjustment_reason TEXT,
                status ENUM('pending', 'confirmed', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
                payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
                notes TEXT,
                admin_notes TEXT,
                estimated_completion_date DATE NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (prebuilt_config_id) REFERENCES pc_prebuilt_configs(id),
                INDEX idx_user (user_id),
                INDEX idx_status (status),
                INDEX idx_payment (payment_status),
                INDEX idx_order_number (order_number),
                INDEX idx_created (created_at)
            )";

            if (mysqli_query($link, $sql)) {
                echo "<p style='color: green;'>✓ Created pc_orders table</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create pc_orders: " . mysqli_error($link) . "</p>";
            }
        }

        // Create credit_audit_log table
        if (in_array('credit_audit_log', $missing_tables)) {
            $sql = "CREATE TABLE credit_audit_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                admin_id INT NOT NULL,
                action_type ENUM('add_credit', 'deduct_credit', 'adjust_credit', 'transfer_credit') NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                source_type VARCHAR(100) NOT NULL COMMENT 'Payment method or transaction source',
                reference_notes TEXT COMMENT 'Transaction reference, payment ID, or admin notes',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user (user_id),
                INDEX idx_admin (admin_id),
                INDEX idx_action (action_type),
                INDEX idx_source (source_type),
                INDEX idx_created (created_at)
            )";

            if (mysqli_query($link, $sql)) {
                echo "<p style='color: green;'>✓ Created credit_audit_log table</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create credit_audit_log: " . mysqli_error($link) . "</p>";
            }
        }
    }

    echo "<h3>Table Check Complete!</h3>";
    echo "<p><a href='admin_pc_management.php'>← Back to PC Management</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

mysqli_close($link);
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
