<!DOCTYPE html>
<html>
<head>
    <title>Modal Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #333; color: white; }
        
        /* Custom Modal Styles */
        .custom-modal {
            display: none;
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.8);
            backdrop-filter: blur(3px);
        }
        
        .custom-modal-content {
            background-color: #2b9869;
            margin: 5% auto;
            padding: 0;
            border: none;
            width: 90%;
            max-width: 600px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .custom-modal-header {
            background-color: #1e6b4a;
            color: white;
            padding: 20px 50px 20px 20px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }
        
        .custom-modal-close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            right: 15px;
            top: 15px;
            z-index: 10000;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.3);
            line-height: 1;
        }
        
        .custom-modal-close:hover {
            color: #fff;
            background-color: rgba(255, 0, 0, 0.7);
            border-color: rgba(255, 255, 255, 0.5);
        }
        
        .custom-modal-close:active {
            transform: scale(0.95);
        }
        
        .custom-modal-body {
            padding: 20px;
            color: white;
        }
        
        .custom-modal-footer {
            padding: 15px 20px;
            text-align: right;
            border-top: 1px solid rgba(255,255,255,0.2);
        }
        
        .custom-modal-btn {
            padding: 10px 20px;
            margin-left: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .custom-modal-btn.primary {
            background-color: #007bff;
            color: white;
        }
        
        .custom-modal-btn.secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .custom-modal-btn:hover {
            opacity: 0.8;
        }
        
        button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>Modal Close Button Test</h1>
    
    <button onclick="testModal1()">Test Modal 1 (Simple)</button>
    <button onclick="testModal2()">Test Modal 2 (Transfer to KMS Credit)</button>
    <button onclick="testModal3()">Test Modal 3 (Complex Content)</button>
    
    <div id="test-results" style="margin-top: 20px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 5px;">
        <h3>Test Results:</h3>
        <p>Click the buttons above to test different modals. Try clicking the X button in the top-right corner of each modal.</p>
        <p id="close-test-result"></p>
    </div>
    
    <script>
    function showModal(id, title, content, buttons = []) {
        // Remove existing modal if any
        const existingModal = document.getElementById('custom-modal');
        if (existingModal) {
            existingModal.remove();
        }
        
        const modal = document.createElement('div');
        modal.id = 'custom-modal';
        modal.className = 'custom-modal';
        modal.style.display = 'block';
        
        const buttonsHtml = buttons.map(btn =>
            `<button class="custom-modal-btn ${btn.class}" onclick="${btn.onclick}">${btn.text}</button>`
        ).join('');
        
        modal.innerHTML = `
            <div class="custom-modal-content">
                <div class="custom-modal-header">
                    <h3>${title}</h3>
                    <span class="custom-modal-close" onclick="closeModal()">&times;</span>
                </div>
                <div class="custom-modal-body">
                    ${content}
                </div>
                <div class="custom-modal-footer">
                    ${buttonsHtml}
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });
        
        // Log that modal was opened
        console.log('Modal opened:', title);
        document.getElementById('close-test-result').innerHTML = 'Modal "' + title + '" opened. Try clicking the X button.';
    }
    
    function closeModal() {
        const modal = document.getElementById('custom-modal');
        if (modal) {
            modal.remove();
            console.log('Modal closed successfully');
            document.getElementById('close-test-result').innerHTML = '<span style="color: #00ff00;">✓ Modal closed successfully!</span>';
        } else {
            console.log('No modal found to close');
            document.getElementById('close-test-result').innerHTML = '<span style="color: #ff6b6b;">✗ No modal found to close</span>';
        }
    }
    
    function testModal1() {
        showModal('test1', 'Simple Test Modal', `
            <p>This is a simple test modal.</p>
            <p>Try clicking the X button in the top-right corner.</p>
        `, [
            { text: 'Cancel', class: 'secondary', onclick: 'closeModal()' },
            { text: 'OK', class: 'primary', onclick: 'closeModal()' }
        ]);
    }
    
    function testModal2() {
        showModal('test2', 'Transfer to KMS Credit', `
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #00ffff;">Available Commission Balance</h3>
                <div style="font-size: 24px; color: #00ff00; font-weight: bold;">$100.00</div>
            </div>
            <div>
                <label>Transfer Amount (USD)</label>
                <input type="number" value="100" style="width: 100%; padding: 10px; margin: 10px 0;">
            </div>
            <div style="background: rgba(0,255,255,0.1); padding: 15px; border-radius: 5px; margin: 15px 0;">
                <h4 style="color: #00ffff; margin: 0 0 10px 0;">Transfer Details</h4>
                <ul style="margin: 0; padding-left: 20px; color: #ccc;">
                    <li>Commission will be transferred to your KMS Credit wallet</li>
                    <li>Transfer is instant and free of charge</li>
                    <li>You can use KMS Credit for all website services</li>
                    <li>This action cannot be undone</li>
                </ul>
            </div>
            <div>
                <label>
                    <input type="checkbox" style="margin-right: 10px;">
                    I confirm that I want to transfer this commission to my KMS Credit wallet
                </label>
            </div>
        `, [
            { text: 'Cancel', class: 'secondary', onclick: 'closeModal()' },
            { text: 'Transfer Now', class: 'primary', onclick: 'closeModal()' }
        ]);
    }
    
    function testModal3() {
        showModal('test3', 'Complex Content Modal', `
            <div style="max-height: 300px; overflow-y: auto;">
                <h4>Complex Content Test</h4>
                <p>This modal has a lot of content to test scrolling and complex layouts.</p>
                <div style="background: rgba(255,255,255,0.1); padding: 15px; margin: 10px 0; border-radius: 5px;">
                    <h5>Section 1</h5>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 15px; margin: 10px 0; border-radius: 5px;">
                    <h5>Section 2</h5>
                    <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 15px; margin: 10px 0; border-radius: 5px;">
                    <h5>Section 3</h5>
                    <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
                </div>
            </div>
        `, [
            { text: 'Close', class: 'secondary', onclick: 'closeModal()' }
        ]);
    }
    
    // Test close button on page load
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Page loaded, modal functions ready');
    });
    </script>
</body>
</html>
