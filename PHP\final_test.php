<?php
session_start();

// Force admin login for testing
$_SESSION["loggedin"] = true;
$_SESSION["id"] = 1;
$_SESSION["username"] = 'admin';
$_SESSION["is_admin"] = true;
?>
<!DOCTYPE html>
<html>
<head>
    <title>Final Test - All Issues</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .test-card { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .test-card h3 { color: #333; margin-top: 0; }
        .status { padding: 5px 10px; border-radius: 4px; font-weight: bold; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 4px; }
        button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; background: #f8f9fa; }
        iframe { width: 100%; height: 400px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>🔧 Final Test - All Reported Issues</h1>
    
    <div class="test-card">
        <h3>1. Admin Profile Page Test</h3>
        <p>Testing if admin profile loads and shows existing data correctly.</p>
        <button onclick="testAdminProfile()">Test Admin Profile</button>
        <div id="admin-profile-result"></div>
    </div>
    
    <div class="test-card">
        <h3>2. Admin PC Management Test</h3>
        <p>Testing if PC management loads categories and components without JSON errors.</p>
        <button onclick="testPCManagement()">Test PC Management</button>
        <div id="pc-management-result"></div>
    </div>
    
    <div class="test-card">
        <h3>3. Member Page Modal Test</h3>
        <p>Testing if Transfer to KMS Credit modal close button works.</p>
        <button onclick="testMemberModal()">Test Member Modal</button>
        <div id="member-modal-result"></div>
    </div>
    
    <div class="test-card">
        <h3>4. PC Builder API Test</h3>
        <p>Testing if Detailed Mode and Pre-built load without JSON errors.</p>
        <button onclick="testPCBuilder()">Test PC Builder APIs</button>
        <div id="pc-builder-result"></div>
    </div>
    
    <div class="test-card">
        <h3>5. Credit Dashboard Test</h3>
        <p>Testing if admin credit dashboard shows member list and add credit functionality.</p>
        <button onclick="testCreditDashboard()">Test Credit Dashboard</button>
        <div id="credit-dashboard-result"></div>
    </div>
    
    <div class="test-card">
        <h3>Database Tables Status</h3>
        <button onclick="checkTables()">Check All Tables</button>
        <div id="tables-result"></div>
    </div>
    
    <script>
    function testAdminProfile() {
        const resultDiv = document.getElementById('admin-profile-result');
        resultDiv.innerHTML = '<div class="warning">Testing admin profile...</div>';
        
        fetch('admin_profile.php')
        .then(response => response.text())
        .then(html => {
            if (html.includes('Admin Profile Management') && !html.includes('Fatal error') && !html.includes('Parse error')) {
                resultDiv.innerHTML = '<div class="success">✓ Admin profile loads successfully</div><iframe srcdoc="' + html.replace(/"/g, '&quot;') + '"></iframe>';
            } else {
                resultDiv.innerHTML = '<div class="error">✗ Admin profile has errors</div><details><summary>View Response</summary><pre>' + html.substring(0, 1000) + '</pre></details>';
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<div class="error">✗ Network error: ' + error.message + '</div>';
        });
    }
    
    function testPCManagement() {
        const resultDiv = document.getElementById('pc-management-result');
        resultDiv.innerHTML = '<div class="warning">Testing PC management APIs...</div>';
        
        // Test categories API
        fetch('pc_components_api.php?action=get_categories')
        .then(response => response.text())
        .then(text => {
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✓ Categories API works: ' + (data.categories ? data.categories.length : 0) + ' categories found</div>';
                    
                    // Test components API
                    return fetch('pc_components_api.php?action=get_components_by_category&active_only=1');
                } else {
                    throw new Error('Categories API failed: ' + data.message);
                }
            } catch (e) {
                throw new Error('JSON parse error in categories: ' + text.substring(0, 200));
            }
        })
        .then(response => response.text())
        .then(text => {
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    const componentCount = data.components_by_category ? Object.keys(data.components_by_category).length : 0;
                    resultDiv.innerHTML += '<div class="success">✓ Components API works: ' + componentCount + ' categories with components</div>';
                } else {
                    throw new Error('Components API failed: ' + data.message);
                }
            } catch (e) {
                throw new Error('JSON parse error in components: ' + text.substring(0, 200));
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<div class="error">✗ PC Management API error: ' + error.message + '</div>';
        });
    }
    
    function testMemberModal() {
        const resultDiv = document.getElementById('member-modal-result');
        resultDiv.innerHTML = '<div class="warning">Testing member page modal...</div>';
        
        fetch('member.php')
        .then(response => response.text())
        .then(html => {
            if (html.includes('closeModal') && html.includes('custom-modal-close') && !html.includes('Fatal error')) {
                resultDiv.innerHTML = '<div class="success">✓ Member page loads with modal functions</div><p><a href="member.php" target="_blank">Open member page to test modal manually</a></p>';
            } else {
                resultDiv.innerHTML = '<div class="error">✗ Member page has issues</div>';
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<div class="error">✗ Member page error: ' + error.message + '</div>';
        });
    }
    
    function testPCBuilder() {
        const resultDiv = document.getElementById('pc-builder-result');
        resultDiv.innerHTML = '<div class="warning">Testing PC builder APIs...</div>';
        
        fetch('pc_components_api.php?action=get_prebuilt_configs&active_only=1')
        .then(response => response.text())
        .then(text => {
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✓ Prebuilt configs API works: ' + (data.configs ? data.configs.length : 0) + ' configs found</div>';
                } else {
                    throw new Error('Prebuilt API failed: ' + data.message);
                }
            } catch (e) {
                throw new Error('JSON parse error: ' + text.substring(0, 200));
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<div class="error">✗ PC Builder API error: ' + error.message + '</div>';
        });
    }
    
    function testCreditDashboard() {
        const resultDiv = document.getElementById('credit-dashboard-result');
        resultDiv.innerHTML = '<div class="warning">Testing credit dashboard...</div>';
        
        fetch('admin_credit.php?action=get_all_wallets&page=1&limit=10')
        .then(response => response.text())
        .then(text => {
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✓ Credit dashboard API works: ' + (data.wallets ? data.wallets.length : 0) + ' wallets found</div>';
                } else {
                    throw new Error('Credit API failed: ' + data.message);
                }
            } catch (e) {
                throw new Error('JSON parse error: ' + text.substring(0, 200));
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<div class="error">✗ Credit dashboard error: ' + error.message + '</div>';
        });
    }
    
    function checkTables() {
        const resultDiv = document.getElementById('tables-result');
        resultDiv.innerHTML = '<div class="warning">Checking database tables...</div>';
        
        fetch('check_pc_tables.php')
        .then(response => response.text())
        .then(html => {
            resultDiv.innerHTML = '<iframe srcdoc="' + html.replace(/"/g, '&quot;') + '"></iframe>';
        })
        .catch(error => {
            resultDiv.innerHTML = '<div class="error">✗ Table check error: ' + error.message + '</div>';
        });
    }
    </script>
</body>
</html>
