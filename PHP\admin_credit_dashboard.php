<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    header("location: ../index.php");
    exit;
}

require_once 'config.php';
require_once 'functions.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Credit Management - Admin Dashboard</title>
    <link rel="stylesheet" href="../CSS/custom-modal.css" />
    <style>
        body { font-family: Arial, sans-serif; background-color: #a48f19; color: white; margin: 0; padding: 20px; }
        .container { max-width: 1400px; margin: auto; background-color: rgb(5 195 182); padding: 10px; border-radius: 10px; box-shadow: 0 2px 8px rgb(0 0 0); }
        h1, h2 { color: #ffffff; text-align: center; font-size: 26px; }

        /* Navigation */
        .nav-controls { text-align: center; margin-bottom: 30px; }
        .nav-controls button {
            padding: 5px 10px;
            margin: 0 5px;
            border: 2px solid rgb(253, 202, 0);
            border-radius: 10px;
            background-color: rgb(253, 202, 0);
            color: #ffffff;
            cursor: pointer;
            font-weight: bold;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
        }
        .nav-controls button:hover {
            background-color: #ffbf00ff;
            border-color: rgba(255, 255, 255, 0.3);
        }
        .nav-controls button.active {
            background-color: #00d2f9;
            border-color: rgba(255, 255, 255, 0.3);
            color: white;
        }
        
        /* Stats Cards */
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border-left: 5px solid rgb(253, 202, 0);
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .stat-card h3 { margin: 0 0 10px 0; color: #ffffff; font-size: 16px; font-weight: bold; }
        .stat-value { font-size: 28px; font-weight: bold; color: #32cd32; }
        .stat-label { font-size: 12px; color: #ffffff; margin-top: 5px; }

        /* Tables */
        .table-container {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .table-container h3 { color: #ffffff; margin-top: 0; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid rgba(255, 255, 255, 0.2); }
        th { background-color: rgba(0, 0, 0, 0.3); color: #ffffff; font-weight: bold; }
        tr:hover { background-color: rgba(255, 255, 255, 0.2); }
        
        /* Buttons */
        .btn {
            padding: 5px 10px;
            border: 2px solid;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            margin: 2px;
            font-weight: bold;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
            border-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }
        .btn-info {
            background-color: #17a2b8;
            color: white;
            border-color: #17a2b8;
        }
        .btn-info:hover {
            background-color: #138496;
            border-color: #117a8b;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #000;
            border-color: #ffc107;
        }
        .btn-warning:hover {
            background-color: #e0a800;
            border-color: #d39e00;
        }
        
        /* Status badges */
        .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 11px; font-weight: bold; }
        .status-pending { background-color: #ffc107; color: #333; }
        .status-processing { background-color: #17a2b8; color: white; }
        .status-completed { background-color: #28a745; color: white; }
        .status-failed { background-color: #dc3545; color: white; }
        .status-cancelled { background-color: #6c757d; color: white; }

        /* Type badges */
        .type-badge { padding: 4px 8px; border-radius: 4px; font-size: 11px; font-weight: bold; }
        .type-admin-gift { background-color: #28a745; color: #fff; }
        .type-admin-deduct { background-color: #dc3545; color: #fff; }
        .type-admin-adjust { background-color: #ffc107; color: #333; }
        .type-deposit { background-color: #17a2b8; color: #fff; }
        .type-spend { background-color: #fd7e14; color: #fff; }
        .type-withdraw { background-color: #e83e8c; color: #fff; }
        .type-refund { background-color: #20c997; color: #fff; }
        .type-transfer-in { background-color: #6f42c1; color: #fff; }
        .type-transfer-out { background-color: #6c757d; color: #fff; }

        /* Source badges */
        .source-badge { padding: 4px 8px; border-radius: 4px; font-size: 11px; font-weight: bold; }
        .source-paypal { background-color: #0070ba; color: #fff; }
        .source-stripe { background-color: #635bff; color: #fff; }
        .source-admin { background-color: #28a745; color: #fff; }
        .source-affiliate { background-color: #fd7e14; color: #fff; }
        .source-cash { background-color: #20c997; color: #fff; }
        .source-bank { background-color: #17a2b8; color: #fff; }
        
        /* Forms */
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; color: #ccc; }
        .form-group input, .form-group select, .form-group textarea { 
            width: 100%; padding: 8px; border-radius: 5px; border: 1px solid #666; 
            background-color: #444; color: white; 
        }
        
        /* Modals */
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.8); }
        .modal-content { background-color: #2c2c2c; margin: 5% auto; padding: 30px; border-radius: 15px; width: 90%; max-width: 600px; }
        .modal h3 { color: #ff4500; text-align: center; margin-bottom: 20px; }
        .close { position: absolute; right: 15px; top: 15px; font-size: 28px; font-weight: bold; color: #ccc; cursor: pointer; }
        .close:hover { color: #ff4500; }
        
        /* Back to Admin button */
        .back-admin-btn {
            position: fixed;
            top: 10px;
            right: 10px;
            background-color: #00bcaa;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 5px 10px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
            text-decoration: none;
        }
        .back-admin-btn:hover {
            background-color: #ffbf00ff;
            border-color: rgba(255, 255, 255, 0.3);
        }
        
        /* Logout button */
        .logout-btn {
            position: fixed; top: 15px; right: 180px; background-color: #e74c3c; color: white;
            border: none; padding: 10px 20px; border-radius: 25px; cursor: pointer; font-size: 14px;
        }
        .logout-btn:hover { background-color: #c0392b; }
        
        /* Pagination */
        .pagination { text-align: center; margin-top: 20px; }
        .pagination button { padding: 8px 12px; margin: 0 2px; border: none; border-radius: 5px; background-color: #444; color: white; cursor: pointer; }
        .pagination button:hover { background-color: #555; }
        .pagination button.active { background-color: #ff4500; }
        
        /* Filters */
        .filters { display: flex; gap: 15px; margin-bottom: 20px; align-items: end; }
        .filters .form-group { margin-bottom: 0; min-width: 150px; }
        
        /* Hidden sections */
        .section { display: none; }
        .section.active { display: block; }
    </style>
</head>
<body>
    <!-- Back to Admin button -->
    <a href="admin.php" class="back-admin-btn">← Back to Admin Panel</a>

    <div class="container">
        <h3>💰 KMS Credit Management Dashboard</h3>
        
        <!-- Navigation -->
        <div class="nav-controls">
            <button onclick="showSection('overview')" class="nav-btn active" data-section="overview">📊 Overview</button>
            <button onclick="showSection('wallets')" class="nav-btn" data-section="wallets">👥 User Wallets</button>
            <button onclick="showSection('deposits')" class="nav-btn" data-section="deposits">💳 Pending Deposits</button>
            <button onclick="showSection('transactions')" class="nav-btn" data-section="transactions">📋 All Transactions</button>
            <button onclick="showSection('settings')" class="nav-btn" data-section="settings">⚙️ Settings</button>
        </div>

        <!-- Overview Section -->
        <div id="overview" class="section active">
            <h2>System Overview</h2>
            <div class="stats-grid" id="statsGrid">
                <!-- Stats will be loaded here -->
            </div>
        </div>

        <!-- User Wallets Section -->
        <div id="wallets" class="section">
            <div class="table-container">
                <h3>User Wallets</h3>
                <div class="filters">
                    <div class="form-group">
                        <label>Search User:</label>
                        <input type="text" id="walletSearch" placeholder="Username or email">
                    </div>
                    <div class="form-group">
                        <button class="btn btn-info" onclick="loadWallets()">Search</button>
                    </div>
                </div>
                <div id="walletsTable">
                    <!-- Wallets table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Pending Deposits Section -->
        <div id="deposits" class="section">
            <div class="table-container">
                <h3>Pending Deposits</h3>
                <div id="depositsTable">
                    <!-- Deposits table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- All Transactions Section -->
        <div id="transactions" class="section">
            <div class="table-container">
                <h3>All Transactions</h3>
                <div class="filters">
                    <div class="form-group">
                        <label>Type:</label>
                        <select id="transactionTypeFilter">
                            <option value="">All Types</option>
                            <option value="deposit">Deposit</option>
                            <option value="withdraw">Withdraw</option>
                            <option value="spend">Spend</option>
                            <option value="refund">Refund</option>
                            <option value="transfer_in">Transfer In</option>
                            <option value="transfer_out">Transfer Out</option>
                            <option value="admin_adjust">Admin Adjust</option>
                            <option value="admin_gift">Admin Gift</option>
                            <option value="admin_deduct">Admin Deduct</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Status:</label>
                        <select id="transactionStatusFilter">
                            <option value="">All Status</option>
                            <option value="pending">Pending</option>
                            <option value="completed">Completed</option>
                            <option value="failed">Failed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-info" onclick="loadTransactions()">Filter</button>
                    </div>
                </div>
                <div id="transactionsTable">
                    <!-- Transactions table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Settings Section -->
        <div id="settings" class="section">
            <div class="table-container">
                <h3>Payment Methods</h3>
                <div id="paymentMethodsTable">
                    <!-- Payment methods will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- Approve Deposit Modal -->
    <div id="approveDepositModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('approveDepositModal')">&times;</span>
            <h3>Approve Deposit</h3>
            <form id="approveDepositForm">
                <input type="hidden" id="approveTransactionId">
                <div class="form-group">
                    <label>Admin Notes:</label>
                    <textarea id="approveNotes" rows="3" placeholder="Optional notes about this approval"></textarea>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-success" style="width: 100%;">Approve Deposit</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Reject Deposit Modal -->
    <div id="rejectDepositModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('rejectDepositModal')">&times;</span>
            <h3>Reject Deposit</h3>
            <form id="rejectDepositForm">
                <input type="hidden" id="rejectTransactionId">
                <div class="form-group">
                    <label>Reason for Rejection:</label>
                    <textarea id="rejectNotes" rows="3" placeholder="Please provide reason for rejection" required></textarea>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-danger" style="width: 100%;">Reject Deposit</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Manual Adjust Modal -->
    <div id="adjustBalanceModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('adjustBalanceModal')">&times;</span>
            <h3>💰 Add KMS Credit</h3>
            <form id="adjustBalanceForm">
                <input type="hidden" id="adjustUserId">
                <div class="form-group">
                    <label>👤 Username:</label>
                    <input type="text" id="adjustUsername" readonly style="background-color: #555;">
                </div>
                <div class="form-group">
                    <label>💵 Credit Amount (USD):</label>
                    <input type="number" id="adjustAmount" step="0.01" min="0.01" required placeholder="Enter amount to add">
                    <small style="color: #ccc;">Enter positive amount to add credit</small>
                </div>
                <div class="form-group">
                    <label>💳 Payment Source / Transaction Type:</label>
                    <select id="adjustSource" required>
                        <option value="">Select payment source...</option>
                        <optgroup label="💳 Payment Methods">
                            <option value="paypal">PayPal</option>
                            <option value="stripe">Stripe (Credit Card)</option>
                            <option value="square">Square</option>
                            <option value="venmo">Venmo</option>
                            <option value="zelle">Zelle</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="cash">Cash Payment</option>
                            <option value="check">Check Payment</option>
                        </optgroup>
                        <optgroup label="🎁 Admin Actions">
                            <option value="admin_gift">Admin Gift</option>
                            <option value="admin_bonus">Admin Bonus</option>
                            <option value="admin_compensation">Admin Compensation</option>
                            <option value="admin_refund">Admin Refund</option>
                            <option value="admin_correction">Admin Correction</option>
                        </optgroup>
                        <optgroup label="🔄 Other Sources">
                            <option value="affiliate_transfer">Affiliate Commission Transfer</option>
                            <option value="service_refund">Service Refund</option>
                            <option value="promotional_credit">Promotional Credit</option>
                            <option value="loyalty_reward">Loyalty Reward</option>
                        </optgroup>
                    </select>
                </div>
                <div class="form-group">
                    <label>📝 Transaction Reference / Notes:</label>
                    <textarea id="adjustReason" rows="3" placeholder="Enter transaction reference, payment ID, or detailed notes for audit trail" required></textarea>
                    <small style="color: #ccc;">Include payment confirmation ID, transaction reference, or reason for admin credit</small>
                </div>
                <div class="form-group" style="background: rgba(0,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">
                    <h4 style="color: #00ffff; margin: 0 0 10px 0;">📊 Audit Information</h4>
                    <p style="margin: 5px 0; color: #ccc; font-size: 14px;">• This transaction will be logged for financial audit</p>
                    <p style="margin: 5px 0; color: #ccc; font-size: 14px;">• Payment source helps track fund origins</p>
                    <p style="margin: 5px 0; color: #ccc; font-size: 14px;">• Reference notes are required for compliance</p>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-success" style="width: 100%; font-size: 16px;">💰 Add KMS Credit</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../JS/custom-modal.js"></script>
    <script>
        // Global variables
        let currentSection = 'overview';
        let currentPage = 1;
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemStats();
            loadPendingDeposits();
        });
        
        // Section navigation
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Remove active class from all nav buttons
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionName).classList.add('active');
            document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');
            
            currentSection = sectionName;
            
            // Load section data
            switch(sectionName) {
                case 'overview':
                    loadSystemStats();
                    break;
                case 'wallets':
                    loadWallets();
                    break;
                case 'deposits':
                    loadPendingDeposits();
                    break;
                case 'transactions':
                    loadTransactions();
                    break;
                case 'settings':
                    loadPaymentMethods();
                    break;
            }
        }
        
        // Load system statistics
        function loadSystemStats() {
            fetch('admin_credit.php?action=get_system_stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.stats;
                        document.getElementById('statsGrid').innerHTML = `
                            <div class="stat-card">
                                <h3>Total Users</h3>
                                <div class="stat-value">${stats.total_users}</div>
                                <div class="stat-label">Active wallets</div>
                            </div>
                            <div class="stat-card">
                                <h3>Total Balance</h3>
                                <div class="stat-value">$${stats.total_balance}</div>
                                <div class="stat-label">In system</div>
                            </div>
                            <div class="stat-card">
                                <h3>Total Deposits</h3>
                                <div class="stat-value">$${stats.total_deposits}</div>
                                <div class="stat-label">All time</div>
                            </div>
                            <div class="stat-card">
                                <h3>Total Spent</h3>
                                <div class="stat-value">$${stats.total_spent}</div>
                                <div class="stat-label">All time</div>
                            </div>
                            <div class="stat-card">
                                <h3>Pending Deposits</h3>
                                <div class="stat-value">${stats.pending_deposits_count}</div>
                                <div class="stat-label">$${stats.pending_deposits_amount}</div>
                            </div>
                            <div class="stat-card">
                                <h3>Recent Activity</h3>
                                <div class="stat-value">${stats.recent_transactions}</div>
                                <div class="stat-label">Last 7 days</div>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error loading stats:', error);
                });
        }
        
        // Logout function
        function confirmLogout() {
            if (confirm('Are you sure you want to logout?')) {
                window.location.href = 'logout.php';
            }
        }
        
        // Modal functions
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        // Load user wallets
        function loadWallets() {
            const search = document.getElementById('walletSearch').value;
            const params = new URLSearchParams({
                action: 'get_all_wallets',
                page: currentPage,
                limit: 20
            });

            if (search) {
                params.append('search', search);
            }

            fetch(`admin_credit.php?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let html = `
                            <table>
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Email</th>
                                        <th>Balance</th>
                                        <th>Frozen</th>
                                        <th>Total Deposited</th>
                                        <th>Total Spent</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;

                        data.wallets.forEach(wallet => {
                            html += `
                                <tr>
                                    <td>${wallet.username}</td>
                                    <td>${wallet.email || 'N/A'}</td>
                                    <td>$${wallet.balance}</td>
                                    <td>$${wallet.frozen_balance}</td>
                                    <td>$${wallet.total_deposited}</td>
                                    <td>$${wallet.total_spent}</td>
                                    <td>
                                        <button class="btn btn-success" onclick="openAdjustModal(${wallet.user_id}, '${wallet.username}')">💰 Add Credit</button>
                                        <button class="btn btn-info" onclick="viewTransactionHistory(${wallet.user_id}, '${wallet.username}')" style="margin-left: 5px;">📊 History</button>
                                    </td>
                                </tr>
                            `;
                        });

                        html += '</tbody></table>';
                        document.getElementById('walletsTable').innerHTML = html;
                    }
                })
                .catch(error => {
                    console.error('Error loading wallets:', error);
                });
        }

        // Load pending deposits
        function loadPendingDeposits() {
            fetch('admin_credit.php?action=get_pending_deposits')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let html = `
                            <table>
                                <thead>
                                    <tr>
                                        <th>Transaction ID</th>
                                        <th>User</th>
                                        <th>Amount</th>
                                        <th>Payment Method</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;

                        data.pending_deposits.forEach(deposit => {
                            html += `
                                <tr>
                                    <td>${deposit.transaction_id}</td>
                                    <td>${deposit.username}</td>
                                    <td>$${deposit.amount}</td>
                                    <td>${deposit.payment_method}</td>
                                    <td><span class="status-badge status-${deposit.status}">${deposit.status}</span></td>
                                    <td>${deposit.created_at}</td>
                                    <td>
                                        <button class="btn btn-success" onclick="openApproveModal('${deposit.transaction_id}')">Approve</button>
                                        <button class="btn btn-danger" onclick="openRejectModal('${deposit.transaction_id}')">Reject</button>
                                    </td>
                                </tr>
                            `;
                        });

                        html += '</tbody></table>';
                        document.getElementById('depositsTable').innerHTML = html;
                    }
                })
                .catch(error => {
                    console.error('Error loading deposits:', error);
                });
        }

        // Load all transactions
        function loadTransactions() {
            const typeFilter = document.getElementById('transactionTypeFilter').value;
            const statusFilter = document.getElementById('transactionStatusFilter').value;

            const params = new URLSearchParams({
                action: 'get_all_transactions',
                page: currentPage,
                limit: 50
            });

            if (typeFilter) params.append('type', typeFilter);
            if (statusFilter) params.append('status', statusFilter);

            fetch(`admin_credit.php?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let html = `
                            <table>
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>User</th>
                                        <th>Type</th>
                                        <th>Amount</th>
                                        <th>Source</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Description</th>
                                        <th>Admin</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;

                        data.transactions.forEach(transaction => {
                            // Format transaction type with better display
                            let typeDisplay = transaction.type;
                            let typeClass = '';

                            switch(transaction.type) {
                                case 'admin_gift':
                                    typeDisplay = '🎁 Admin Gift';
                                    typeClass = 'type-admin-gift';
                                    break;
                                case 'admin_deduct':
                                    typeDisplay = '⚠️ Admin Deduct';
                                    typeClass = 'type-admin-deduct';
                                    break;
                                case 'admin_adjust':
                                    typeDisplay = '⚙️ Admin Adjust';
                                    typeClass = 'type-admin-adjust';
                                    break;
                                case 'deposit':
                                    typeDisplay = '💰 Deposit';
                                    typeClass = 'type-deposit';
                                    break;
                                case 'spend':
                                    typeDisplay = '🛒 Spend';
                                    typeClass = 'type-spend';
                                    break;
                                case 'withdraw':
                                    typeDisplay = '💸 Withdraw';
                                    typeClass = 'type-withdraw';
                                    break;
                                case 'refund':
                                    typeDisplay = '↩️ Refund';
                                    typeClass = 'type-refund';
                                    break;
                                case 'transfer_in':
                                    typeDisplay = '📥 Transfer In';
                                    typeClass = 'type-transfer-in';
                                    break;
                                case 'transfer_out':
                                    typeDisplay = '📤 Transfer Out';
                                    typeClass = 'type-transfer-out';
                                    break;
                            }

                            // Format source display
                            let sourceDisplay = transaction.payment_method || transaction.source || 'N/A';
                            let sourceClass = '';

                            // Add icons and styling for different sources
                            if (sourceDisplay.includes('paypal')) {
                                sourceDisplay = '💳 PayPal';
                                sourceClass = 'source-paypal';
                            } else if (sourceDisplay.includes('stripe')) {
                                sourceDisplay = '💳 Stripe';
                                sourceClass = 'source-stripe';
                            } else if (sourceDisplay.includes('admin')) {
                                sourceDisplay = '👨‍💼 Admin';
                                sourceClass = 'source-admin';
                            } else if (sourceDisplay.includes('affiliate')) {
                                sourceDisplay = '🤝 Affiliate';
                                sourceClass = 'source-affiliate';
                            }

                            html += `
                                <tr>
                                    <td>${transaction.transaction_id}</td>
                                    <td>${transaction.username}</td>
                                    <td><span class="type-badge ${typeClass}">${typeDisplay}</span></td>
                                    <td>$${transaction.amount}</td>
                                    <td><span class="source-badge ${sourceClass}">${sourceDisplay}</span></td>
                                    <td><span class="status-badge status-${transaction.status}">${transaction.status}</span></td>
                                    <td>${transaction.created_at}</td>
                                    <td>${transaction.description || 'N/A'}</td>
                                    <td>${transaction.admin_username || 'System'}</td>
                                </tr>
                            `;
                        });

                        html += '</tbody></table>';
                        document.getElementById('transactionsTable').innerHTML = html;
                    }
                })
                .catch(error => {
                    console.error('Error loading transactions:', error);
                });
        }

        // Load payment methods
        function loadPaymentMethods() {
            // This would load payment methods configuration
            // For now, just show a placeholder
            document.getElementById('paymentMethodsTable').innerHTML = `
                <p>Payment methods configuration will be implemented here.</p>
                <p>Current active methods: PayPal, Stripe, Bank Transfer, Admin Manual</p>
            `;
        }

        // Modal functions
        function openApproveModal(transactionId) {
            document.getElementById('approveTransactionId').value = transactionId;
            document.getElementById('approveDepositModal').style.display = 'block';
        }

        function openRejectModal(transactionId) {
            document.getElementById('rejectTransactionId').value = transactionId;
            document.getElementById('rejectDepositModal').style.display = 'block';
        }

        function openAdjustModal(userId, username) {
            document.getElementById('adjustUserId').value = userId;
            document.getElementById('adjustUsername').value = username;
            document.getElementById('adjustBalanceModal').style.display = 'block';
        }

        function viewTransactionHistory(userId, username) {
            // Switch to transactions section and filter by user
            showSection('transactions');
            // Add user filter functionality here if needed
            loadTransactions(userId);
        }

        // Form submissions
        document.getElementById('approveDepositForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('action', 'approve_deposit');
            formData.append('transaction_id', document.getElementById('approveTransactionId').value);
            formData.append('admin_notes', document.getElementById('approveNotes').value);

            fetch('admin_credit.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Deposit approved successfully!');
                    closeModal('approveDepositModal');
                    loadPendingDeposits();
                    loadSystemStats();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error approving deposit:', error);
                alert('Network error occurred');
            });
        });

        document.getElementById('rejectDepositForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('action', 'reject_deposit');
            formData.append('transaction_id', document.getElementById('rejectTransactionId').value);
            formData.append('admin_notes', document.getElementById('rejectNotes').value);

            fetch('admin_credit.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Deposit rejected successfully!');
                    closeModal('rejectDepositModal');
                    loadPendingDeposits();
                    loadSystemStats();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error rejecting deposit:', error);
                alert('Network error occurred');
            });
        });

        document.getElementById('adjustBalanceForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const amount = parseFloat(document.getElementById('adjustAmount').value);
            const source = document.getElementById('adjustSource').value;
            const reason = document.getElementById('adjustReason').value;

            // Validation
            if (amount <= 0) {
                alert('Please enter a positive amount');
                return;
            }

            if (!source) {
                alert('Please select a payment source/transaction type');
                return;
            }

            if (!reason.trim()) {
                alert('Please enter transaction reference or notes');
                return;
            }

            const formData = new FormData();
            formData.append('action', 'add_kms_credit');
            formData.append('user_id', document.getElementById('adjustUserId').value);
            formData.append('amount', amount);
            formData.append('source', source);
            formData.append('reason', reason);

            fetch('admin_credit.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('KMS Credit added successfully!');
                    closeModal('adjustBalanceModal');
                    loadWallets();
                    loadSystemStats();
                    // Clear form
                    document.getElementById('adjustBalanceForm').reset();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error adding KMS credit:', error);
                alert('Network error occurred');
            });
        });

        // Click outside modal to close
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
